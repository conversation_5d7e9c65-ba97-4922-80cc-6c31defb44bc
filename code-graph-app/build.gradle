plugins {
    id 'application'
}

description = 'code Graph App - 主应用程序'

application {
    mainClass = 'com.puti.code.graph.app.Main'
}

dependencies {
    // 依赖所有模块
    implementation project(':code-graph-base')
    implementation project(':code-graph-repository')
    implementation project(':code-graph-ai')
    implementation project(':code-graph-analyzer:code-graph-analyzer-java')
    implementation project(':code-graph-documentation')

    // Apache POI for Excel export
    implementation 'org.apache.poi:poi:5.4.0'
    implementation 'org.apache.poi:poi-ooxml:5.4.0'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}

// 创建可执行的fat jar
jar {
    manifest {
        attributes 'Main-Class': application.mainClass
    }
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
