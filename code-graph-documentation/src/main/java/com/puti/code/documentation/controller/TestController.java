package com.puti.code.documentation.controller;

import com.puti.code.base.entity.rdb.DocumentationAggregation;
import com.puti.code.documentation.repository.sql.mapper.DocumentationAggregationMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Resource
    private DocumentationAggregationMapper mapper;

    @GetMapping("/batchInsert")
    public void test(){
        List<DocumentationAggregation> documentationAggregationList = new ArrayList<>();
        DocumentationAggregation documentationAggregation = new DocumentationAggregation();
        documentationAggregation.setAggregatedDocumentationId("111");
        documentationAggregation.setAggregatedDocumentationId("111");
        documentationAggregation.setDocumentationId("111");
        documentationAggregation.setEntryPointId("111");
        documentationAggregation.setEntryPointName("xxx");
        documentationAggregation.setWeight(1);
        documentationAggregation.setCreatedAt(LocalDateTime.now());
        documentationAggregationList.add(documentationAggregation);
        int result = mapper.batchInsert(documentationAggregationList);
    }
}
