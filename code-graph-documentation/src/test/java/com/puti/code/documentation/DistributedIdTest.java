package com.puti.code.documentation;

import com.puti.code.base.entity.rdb.Documentation;
import com.puti.code.documentation.global.DistributedIdGenerator;
import com.puti.code.documentation.repository.sql.mapper.DocumentationMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

/**
 * 分布式ID生成测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DistributedIdTest {
    
    @Autowired
    private DistributedIdGenerator idGenerator;
    
    @Autowired
    private DocumentationMapper documentationMapper;
    
    @Test
    public void testIdGeneration() {
        // 测试雪花算法ID生成
        String snowflakeId = idGenerator.generateId();
        log.info("生成的雪花算法ID: {}", snowflakeId);
        
        // 测试UUID生成
        String uuidId = idGenerator.generateId(com.puti.code.base.annotation.AutoKey.KeyStrategy.UUID);
        log.info("生成的UUID: {}", uuidId);
        
        // 验证ID的唯一性
        for (int i = 0; i < 10; i++) {
            String id1 = idGenerator.generateId();
            String id2 = idGenerator.generateId();
            assert !id1.equals(id2) : "生成的ID不应该重复";
            log.info("第{}次生成的ID: {}, {}", i + 1, id1, id2);
        }
    }
    
    @Test
    public void testAutoKeyAnnotation() {
        // 创建一个Documentation对象，不设置ID
        Documentation doc = Documentation.builder()
                .entryPointId("test-entry-point")
                .entryPointName("测试入口点")
                .title("测试说明书")
                .summary("这是一个测试说明书")
                .content("测试内容")
                .level(1)
                .status(Documentation.DocumentationStatus.PENDING)
                .version(1)
                .isFinalVersion(false)
                .projectId("test-project")
                .branchName("main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        log.info("插入前的Documentation ID: {}", doc.getId());
        
        // 插入数据库，应该自动生成ID
        int result = documentationMapper.insert(doc);
        
        log.info("插入结果: {}", result);
        log.info("插入后的Documentation ID: {}", doc.getId());
        
        // 验证ID已经被自动生成
        assert doc.getId() != null : "ID应该被自动生成";
        assert !doc.getId().isEmpty() : "ID不应该为空";
    }
}
